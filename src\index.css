@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 0 75% 56%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 0 75% 56%;

    /* GlamSpot Design System */
    --glamspot-primary: 0 75% 56%;
    --glamspot-primary-light: 0 65% 65%;
    --glamspot-primary-dark: 0 85% 45%;
    --glamspot-accent: 45 100% 60%;
    --glamspot-neutral-50: 0 0% 98%;
    --glamspot-neutral-100: 0 0% 96%;
    --glamspot-neutral-200: 0 0% 90%;
    --glamspot-neutral-500: 0 0% 60%;
    --glamspot-neutral-700: 0 0% 40%;
    --glamspot-neutral-900: 0 0% 10%;

    /* Shadows */
    --shadow-soft: 0 2px 8px -2px hsl(var(--glamspot-neutral-900) / 0.1);
    --shadow-medium: 0 4px 16px -4px hsl(var(--glamspot-neutral-900) / 0.15);
    --shadow-strong: 0 8px 24px -8px hsl(var(--glamspot-neutral-900) / 0.2);

    /* Transitions */
    --transition-smooth: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Raleway', sans-serif;
  }

  /* Typography - Headings use Cinzel, paragraphs use Raleway */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Cinzel', serif;
    font-weight: 600;
  }

  p, span, div, label, input, textarea, button {
    font-family: 'Raleway', sans-serif;
  }

  /* Specific heading weights */
  h1 {
    font-weight: 700;
  }

  h2 {
    font-weight: 600;
  }

  h3, h4 {
    font-weight: 500;
  }

  h5, h6 {
    font-weight: 400;
  }
}
